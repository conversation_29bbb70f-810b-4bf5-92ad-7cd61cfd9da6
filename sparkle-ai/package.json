{"name": "sparkle-ai", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@babel/core": "^7.28.0", "@tauri-apps/api": "^2.6.0", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-shell": "^2.3.0", "@types/echarts": "^5.0.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "highlight.js": "^11.11.1", "katex": "^0.16.22", "mermaid": "^11.8.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "react-router-dom": "^7.6.3", "rehype-highlight": "^7.0.2", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "zustand": "^5.0.6"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/node": "^24.0.14", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.0", "typescript": "~5.6.2", "vite": "^6.0.3"}}