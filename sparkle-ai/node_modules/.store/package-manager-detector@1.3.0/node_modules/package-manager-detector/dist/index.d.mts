export { COMMANDS, constructCommand, resolveCommand } from './commands.mjs';
export { AGENTS, INSTALL_METADATA, INSTALL_PAGE, LOCKS } from './constants.mjs';
export { detect, getUserAgent } from './detect.mjs';
export { A as Agent, b as AgentCommandValue, c as AgentCommands, a as AgentName, C as Command, d as DetectOptions, e as DetectResult, D as DetectStrategy, R as ResolvedCommand } from './shared/package-manager-detector.pUYRhiOu.mjs';
