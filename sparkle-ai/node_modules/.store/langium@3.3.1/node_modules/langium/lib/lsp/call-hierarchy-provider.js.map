{"version": 3, "file": "call-hierarchy-provider.js", "sourceRoot": "", "sources": ["../../src/lsp/call-hierarchy-provider.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAahF,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,2BAA2B,EAAE,MAAM,uBAAuB,CAAC;AACpE,OAAO,EAAE,GAAG,EAAE,MAAM,uBAAuB,CAAC;AAa5C,MAAM,OAAgB,6BAA6B;IAM/C,YAAY,QAAyB;QACjC,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC;QACnD,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC;QACrD,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAC5D,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC;IACrD,CAAC;IAED,oBAAoB,CAAC,QAAkC,EAAE,MAAkC;QACvF,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC;QAC5C,MAAM,UAAU,GAAG,2BAA2B,CAC1C,QAAQ,CAAC,QAAQ,EACjB,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,EAC/C,IAAI,CAAC,aAAa,CAAC,UAAU,CAChC,CAAC;QACF,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,OAAO,SAAS,CAAC;QACrB,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QACxE,IAAI,CAAC,eAAe,EAAE,CAAC;YACnB,OAAO,SAAS,CAAC;QACrB,CAAC;QAED,OAAO,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IACzE,CAAC;IAES,qBAAqB,CAAC,UAAmB,EAAE,QAAkC;QACnF,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC3D,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACnD,IAAI,CAAC,QAAQ,IAAI,CAAC,UAAU,CAAC,QAAQ,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;YAC1D,OAAO,SAAS,CAAC;QACrB,CAAC;QAED,OAAO,iBACH,IAAI,EAAE,UAAU,CAAC,MAAM,EACvB,IAAI,EACJ,KAAK,EAAE,UAAU,CAAC,QAAQ,CAAC,KAAK,EAChC,cAAc,EAAE,QAAQ,CAAC,KAAK,EAC9B,GAAG,EAAE,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,IACzB,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,EAC1C,CAAC;IACP,CAAC;IAES,oBAAoB,CAAC,WAAoB;QAC/C,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,MAAwC;QACxD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QACtF,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC;QAC5C,MAAM,UAAU,GAAG,2BAA2B,CAC1C,QAAQ,CAAC,QAAQ,EACjB,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EACvD,IAAI,CAAC,aAAa,CAAC,UAAU,CAChC,CAAC;QACF,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,OAAO,SAAS,CAAC;QACrB,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAC7C,UAAU,CAAC,OAAO,EAClB;YACI,kBAAkB,EAAE,KAAK;SAC5B,CACJ,CAAC;QACF,OAAO,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IACjE,CAAC;IAOD,KAAK,CAAC,aAAa,CAAC,MAAwC;QACxD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QACtF,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC;QAC5C,MAAM,UAAU,GAAG,2BAA2B,CAC1C,QAAQ,CAAC,QAAQ,EACjB,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EACvD,IAAI,CAAC,aAAa,CAAC,UAAU,CAChC,CAAC;QACF,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,OAAO,SAAS,CAAC;QACrB,CAAC;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IACrD,CAAC;CAMJ"}