{"version": 3, "file": "syntax-tree.js", "sourceRoot": "", "sources": ["../src/syntax-tree.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAyBhF,MAAM,UAAU,SAAS,CAAC,GAAY;IAClC,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI,IAAI,OAAQ,GAAe,CAAC,KAAK,KAAK,QAAQ,CAAC;AACjG,CAAC;AAkCD,MAAM,UAAU,WAAW,CAAC,GAAY;IACpC,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI,IAAI,OAAQ,GAAiB,CAAC,QAAQ,KAAK,QAAQ,CAAC;AACtG,CAAC;AA8BD,MAAM,UAAU,oBAAoB,CAAC,GAAY;IAC7C,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI;WACvC,OAAQ,GAA0B,CAAC,IAAI,KAAK,QAAQ;WACpD,OAAQ,GAA0B,CAAC,IAAI,KAAK,QAAQ;WACpD,OAAQ,GAA0B,CAAC,IAAI,KAAK,QAAQ,CAAC;AAChE,CAAC;AAqBD,MAAM,UAAU,cAAc,CAAC,GAAY;IACvC,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI;WACvC,SAAS,CAAE,GAAoB,CAAC,SAAS,CAAC;WAC1C,WAAW,CAAE,GAAoB,CAAC,SAAS,CAAC;WAC5C,OAAQ,GAAoB,CAAC,OAAO,KAAK,QAAQ,CAAC;AAC7D,CAAC;AAeD;;;GAGG;AACH,MAAM,OAAgB,qBAAqB;IAA3C;QAEc,aAAQ,GAAwD,EAAE,CAAC;QACnE,gBAAW,GAAyC,EAAE,CAAC;IA6CrE,CAAC;IAtCG,UAAU,CAAC,IAAa,EAAE,IAAY;QAClC,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAC/D,CAAC;IAED,SAAS,CAAC,OAAe,EAAE,SAAiB;QACxC,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,IAAI,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACpC,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;QACzC,CAAC;QACD,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;QACnC,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YACzB,OAAO,QAAQ,CAAC;QACpB,CAAC;aAAM,CAAC;YACJ,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YACzD,MAAM,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC;YAC3B,OAAO,MAAM,CAAC;QAClB,CAAC;IACL,CAAC;IAED,cAAc,CAAC,IAAY;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,QAAQ,EAAE,CAAC;YACX,OAAO,QAAQ,CAAC;QACpB,CAAC;aAAM,CAAC;YACJ,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,KAAK,GAAa,EAAE,CAAC;YAC3B,KAAK,MAAM,eAAe,IAAI,QAAQ,EAAE,CAAC;gBACrC,IAAI,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,EAAE,CAAC;oBACxC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;gBAChC,CAAC;YACL,CAAC;YACD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;YAC/B,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;CACJ;AA6DD,MAAM,UAAU,kBAAkB,CAAC,IAAa;IAC5C,OAAO,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,CAAC,OAAO,CAAE,IAAyB,CAAC,OAAO,CAAC,CAAC;AAC1G,CAAC;AASD,MAAM,UAAU,aAAa,CAAC,IAAa;IACvC,OAAO,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,IAAI,IAAI,OAAQ,IAAoB,CAAC,SAAS,KAAK,QAAQ,CAAC;AAC5G,CAAC;AAMD,MAAM,UAAU,aAAa,CAAC,IAAa;IACvC,OAAO,kBAAkB,CAAC,IAAI,CAAC,IAAI,OAAQ,IAAoB,CAAC,QAAQ,KAAK,QAAQ,CAAC;AAC1F,CAAC"}