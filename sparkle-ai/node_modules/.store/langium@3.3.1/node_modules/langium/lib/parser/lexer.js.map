{"version": 3, "file": "lexer.js", "sourceRoot": "", "sources": ["../../src/parser/lexer.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAIhF,OAAO,EAAE,KAAK,IAAI,eAAe,EAAE,yBAAyB,EAAE,MAAM,YAAY,CAAC;AAGjF,MAAM,OAAO,gCAAgC;IAEzC,gCAAgC,CAAC,QAAgB,EAAE,WAAmB,EAAE,MAAc,EAAE,IAAa,EAAE,MAAe;QAClH,OAAO,yBAAyB,CAAC,gCAAgC,CAAC,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IACnH,CAAC;IAED,gCAAgC,CAAC,KAAa;QAC1C,OAAO,yBAAyB,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAC;IAC7E,CAAC;CACJ;AAwBD,MAAM,CAAC,MAAM,wBAAwB,GAAoB,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;AAO1E,MAAM,OAAO,YAAY;IAOrB,YAAY,QAA6B;QACrC,IAAI,CAAC,oBAAoB,GAAG,QAAQ,CAAC,MAAM,CAAC,yBAAyB,CAAC;QACtE,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC;QACjD,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,EAAE;YAC3D,eAAe,EAAE,QAAQ,CAAC,gBAAgB,CAAC,eAAe;SAC7D,CAAC,CAAC;QACH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QACrD,MAAM,WAAW,GAAG,qBAAqB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QACnF,MAAM,UAAU,GAAG,QAAQ,CAAC,gBAAgB,CAAC,IAAI,KAAK,YAAY,CAAC;QACnE,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,CAAC,WAAW,EAAE;YACpD,gBAAgB,EAAE,MAAM;YACxB,eAAe,EAAE,UAAU;YAC3B,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;SAClD,CAAC,CAAC;IACP,CAAC;IAED,IAAI,UAAU;QACV,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,QAAQ,CAAC,IAAY,EAAE,WAA4B,wBAAwB;;QACvE,MAAM,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC7D,OAAO;YACH,MAAM,EAAE,gBAAgB,CAAC,MAAM;YAC/B,MAAM,EAAE,gBAAgB,CAAC,MAAM;YAC/B,MAAM,EAAE,MAAA,gBAAgB,CAAC,MAAM,CAAC,MAAM,mCAAI,EAAE;YAC5C,MAAM,EAAE,MAAA,MAAA,IAAI,CAAC,YAAY,EAAC,iBAAiB,mDAAG,IAAI,CAAC;SACtD,CAAC;IACN,CAAC;IAES,qBAAqB,CAAC,WAA4B;QACxD,IAAI,qBAAqB,CAAC,WAAW,CAAC;YAAE,OAAO,WAAW,CAAC;QAC3D,MAAM,MAAM,GAAG,2BAA2B,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC;QAChH,MAAM,GAAG,GAAwB,EAAE,CAAC;QACpC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;QACjD,OAAO,GAAG,CAAC;IACf,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,UAAU,gBAAgB,CAAC,eAAgC;IAC7D,OAAO,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,IAAI,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5G,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,2BAA2B,CAAC,eAAgC;IACxE,OAAO,eAAe,IAAI,OAAO,IAAI,eAAe,IAAI,aAAa,IAAI,eAAe,CAAC;AAC7F,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,qBAAqB,CAAC,eAAgC;IAClE,OAAO,CAAC,gBAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,CAAC;AAC/F,CAAC"}