{"version": 3, "file": "uri-utils.js", "sourceRoot": "", "sources": ["../../src/utils/uri-utils.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAEhF,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AAExC,OAAO,EAAE,GAAG,EAAE,CAAC;AAEf,MAAM,KAAW,QAAQ,CAgCxB;AAhCD,WAAiB,QAAQ;IAER,iBAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;IAC1B,gBAAO,GAAG,KAAK,CAAC,OAAO,CAAC;IACxB,gBAAO,GAAG,KAAK,CAAC,OAAO,CAAC;IACxB,iBAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;IAC1B,oBAAW,GAAG,KAAK,CAAC,WAAW,CAAC;IAE7C,SAAgB,MAAM,CAAC,CAAgB,EAAE,CAAgB;QACrD,OAAO,CAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,QAAQ,EAAE,OAAK,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,QAAQ,EAAE,CAAA,CAAC;IAC3C,CAAC;IAFe,eAAM,SAErB,CAAA;IAED,SAAgB,QAAQ,CAAC,IAAkB,EAAE,EAAgB;QACzD,MAAM,QAAQ,GAAG,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;QAC7D,MAAM,MAAM,GAAG,OAAO,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC;QACrD,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAChE,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC5D,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,OAAO,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/B,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC9B,MAAM;YACV,CAAC;QACL,CAAC;QACD,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACpD,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1C,OAAO,QAAQ,GAAG,MAAM,CAAC;IAC7B,CAAC;IAde,iBAAQ,WAcvB,CAAA;IAED,SAAgB,SAAS,CAAC,GAAiB;QACvC,OAAO,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;IAChD,CAAC;IAFe,kBAAS,YAExB,CAAA;AAEL,CAAC,EAhCgB,QAAQ,KAAR,QAAQ,QAgCxB"}