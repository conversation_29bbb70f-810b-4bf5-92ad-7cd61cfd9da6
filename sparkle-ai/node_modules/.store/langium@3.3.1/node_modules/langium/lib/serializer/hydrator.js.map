{"version": 3, "file": "hydrator.js", "sourceRoot": "", "sources": ["../../src/serializer/hydrator.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAKhF,OAAO,EAAE,oBAAoB,EAAE,eAAe,EAAE,eAAe,EAAE,MAAM,+BAA+B,CAAC;AACvG,OAAO,EAAE,iBAAiB,EAAsC,MAAM,+BAA+B,CAAC;AAMtG,OAAO,EAAE,aAAa,EAAE,kBAAkB,EAAE,aAAa,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAC7G,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAClD,OAAO,EAAE,KAAK,EAAE,MAAM,yBAAyB,CAAC;AAChD,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AA4BlD,MAAM,OAAO,eAAe;IASxB,YAAY,QAA6B;QAHtB,wBAAmB,GAAG,IAAI,KAAK,EAA2B,CAAC;QAC3D,mBAAc,GAAG,IAAI,KAAK,EAAqB,CAAC;QAG/D,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;QAChC,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC;QACnC,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC;IAC7C,CAAC;IAED,SAAS,CAAC,MAA4B;QAClC,OAAO;YACH,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,WAAW,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS;YAC3F,iDAAiD;YACjD,sGAAsG;YACtG,YAAY,EAAE,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,iCAAM,CAAC,KAAE,OAAO,EAAE,CAAC,CAAC,OAAO,IAAG,CAAC;YAC1E,KAAK,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SACzF,CAAC;IACN,CAAC;IAES,oBAAoB,CAAC,WAAyB;QACpD,6CAA6C;QAC7C,OAAO,WAAW,CAAC;IACvB,CAAC;IAES,uBAAuB,CAAC,IAAa;QAC3C,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAgB,CAAC;QACzC,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAgB,CAAC;QACzC,KAAK,MAAM,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;YACpC,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QAC9B,CAAC;QACD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,KAAK,MAAM,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7C,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YAC9B,CAAC;QACL,CAAC;QACD,OAAO;YACH,QAAQ;YACR,QAAQ;SACX,CAAC;IACN,CAAC;IAES,gBAAgB,CAAC,IAAa,EAAE,OAAyB;QAC/D,MAAM,GAAG,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAwB,CAAC;QAC9D,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACvB,GAAG,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAC3C,GAAG,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACjD,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC9B,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACjE,CAAC;QACD,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/C,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBACvB,SAAS;YACb,CAAC;YACD,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBACvB,MAAM,GAAG,GAAU,EAAE,CAAC;gBACtB,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;gBAChB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;oBACvB,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;wBAClB,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;oBACnD,CAAC;yBAAM,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;wBAC3B,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;oBACrD,CAAC;yBAAM,CAAC;wBACJ,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACnB,CAAC;gBACL,CAAC;YACL,CAAC;iBAAM,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC1B,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YACtD,CAAC;iBAAM,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC5B,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YACxD,CAAC;iBAAM,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC7B,GAAG,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;YACtB,CAAC;QACL,CAAC;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IAES,kBAAkB,CAAC,SAAoB,EAAE,OAAyB;QACxE,MAAM,GAAG,GAA4B,EAAE,CAAC;QACxC,GAAG,CAAC,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;QAClC,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;YACrB,GAAG,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC5D,CAAC;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IAES,gBAAgB,CAAC,IAAa,EAAE,OAAyB;QAC/D,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAwB,CAAC;QAClE,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;YACtB,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QACrC,CAAC;aAAM,CAAC;YACJ,gEAAgE;YAChE,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACzE,CAAC;QACD,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC7B,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACrD,IAAI,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;QACvF,CAAC;aAAM,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7B,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YACxC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAC7B,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAC7B,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC;YAC1C,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC;YACjD,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC;YACtC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC;QACjD,CAAC;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,OAAO,CAA8B,MAA2B;QAC5D,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC;QAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAClD,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;YACrB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAChD,CAAC;QACD,OAAO;YACH,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,YAAY,EAAE,MAAM,CAAC,YAAY;YACjC,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAM;SACjD,CAAC;IACN,CAAC;IAES,sBAAsB,CAAC,IAAS;QACtC,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAgB,CAAC;QACzC,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAgB,CAAC;QACzC,KAAK,MAAM,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;YACpC,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,EAAa,CAAC,CAAC;QACzC,CAAC;QACD,IAAI,IAAiB,CAAC;QACtB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,KAAK,MAAM,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7C,IAAI,GAAiC,CAAC;gBACtC,IAAI,UAAU,IAAI,OAAO,EAAE,CAAC;oBACxB,GAAG,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,QAAkB,CAAC,CAAC;oBACtD,IAAI,GAAG,GAAkB,CAAC;gBAC9B,CAAC;qBAAM,IAAI,SAAS,IAAI,OAAO,EAAE,CAAC;oBAC9B,GAAG,GAAG,IAAI,oBAAoB,EAAE,CAAC;gBACrC,CAAC;qBAAM,IAAI,WAAW,IAAI,OAAO,EAAE,CAAC;oBAChC,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;gBAC3C,CAAC;gBACD,IAAI,GAAG,EAAE,CAAC;oBACN,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;oBAC3B,GAAG,CAAC,IAAI,GAAG,IAAK,CAAC;gBACrB,CAAC;YACL,CAAC;QACL,CAAC;QACD,OAAO;YACH,QAAQ;YACR,QAAQ;SACX,CAAC;IACN,CAAC;IAES,cAAc,CAAC,IAAS,EAAE,OAAuB;QACvD,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAA4B,CAAC;QACtE,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAC3B,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAC/C,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACrD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3D,CAAC;QACD,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/C,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBACvB,SAAS;YACb,CAAC;YACD,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBACvB,MAAM,GAAG,GAAc,EAAE,CAAC;gBAC1B,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;gBACpB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;oBACvB,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;wBAClB,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;oBAC1E,CAAC;yBAAM,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;wBAC3B,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;oBAClE,CAAC;yBAAM,CAAC;wBACJ,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACnB,CAAC;gBACL,CAAC;YACL,CAAC;iBAAM,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC1B,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC;YACjF,CAAC;iBAAM,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC5B,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YACzE,CAAC;iBAAM,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC7B,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;YAC1B,CAAC;QACL,CAAC;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAES,SAAS,CAAC,IAAS,EAAE,MAAW;QACtC,IAAI,CAAC,UAAU,GAAG,MAAiB,CAAC;QACpC,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,gBAAgB,CAAC,SAAc,EAAE,IAAa,EAAE,IAAY,EAAE,OAAuB;QAC3F,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAE,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;IACjH,CAAC;IAES,cAAc,CAAC,OAAY,EAAE,OAAuB,EAAE,GAAG,GAAG,CAAC;QACnE,MAAM,UAAU,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAqB,CAAC;QACrE,IAAI,OAAO,OAAO,CAAC,aAAa,KAAK,QAAQ,EAAE,CAAC;YAC5C,UAAU,CAAC,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAC7E,CAAC;QACD,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAE,CAAC;QAC5D,IAAI,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YACjC,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;gBAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC;gBAC5D,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtC,CAAC;QACL,CAAC;QACD,OAAO,UAAU,CAAC;IACtB,CAAC;IAES,kBAAkB,CAAC,OAAY;QACrC,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACvD,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC9B,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC9B,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACpC,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACxC,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAChC,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACpC,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC9B,MAAM,IAAI,GAAG,IAAI,eAAe,CAC5B,MAAM,EACN,MAAM,EACN;YACI,KAAK,EAAE;gBACH,IAAI,EAAE,SAAS;gBACf,SAAS,EAAE,WAAW;aACzB;YACD,GAAG,EAAE;gBACD,IAAI,EAAE,OAAO;gBACb,SAAS,EAAE,SAAS;aACvB;SACJ,EACD,SAAS,EACT,MAAM,CACT,CAAC;QACF,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,YAAY,CAAC,IAAY;QAC/B,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAES,mBAAmB,CAAC,IAAiC;QAC3D,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,OAAO,SAAS,CAAC;QACrB,CAAC;QACD,IAAI,IAAI,CAAC,mBAAmB,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YACtC,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACrC,CAAC;QACD,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC9C,CAAC;IAES,iBAAiB,CAAC,EAAU;QAClC,IAAI,IAAI,CAAC,mBAAmB,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YACtC,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACrC,CAAC;QACD,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACpD,OAAO,OAAO,CAAC;IACnB,CAAC;IAES,yBAAyB;QAC/B,IAAI,EAAE,GAAG,CAAC,CAAC;QACX,KAAK,MAAM,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5C,IAAI,iBAAiB,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC7B,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC;YAChD,CAAC;QACL,CAAC;IACL,CAAC;CAEJ"}