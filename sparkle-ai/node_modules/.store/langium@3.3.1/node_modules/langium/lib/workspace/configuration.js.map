{"version": 3, "file": "configuration.js", "sourceRoot": "", "sources": ["../../src/workspace/configuration.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAEhF,OAAO,EAAE,OAAO,EAAE,MAAM,mBAAmB,CAAC;AAY5C,OAAO,EAAE,QAAQ,EAAE,MAAM,2BAA2B,CAAC;AAgErD;;GAEG;AACH,MAAM,OAAO,4BAA4B;IAQrC,YAAY,QAAmC;QAL5B,WAAM,GAAG,IAAI,QAAQ,EAAQ,CAAC;QACvC,aAAQ,GAAwC,EAAE,CAAC;QACnD,oBAAe,GAAG,KAAK,CAAC;QACxB,wCAAmC,GAAG,IAAI,OAAO,EAA8B,CAAC;QAGtF,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,eAAe,CAAC;IACpD,CAAC;IAED,IAAI,KAAK;QACL,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;IAC/B,CAAC;IAED,UAAU,CAAC,MAAwB;;QAC/B,IAAI,CAAC,eAAe,GAAG,MAAA,MAAA,MAAM,CAAC,YAAY,CAAC,SAAS,0CAAE,aAAa,mCAAI,KAAK,CAAC;IACjF,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,MAAsC;QACpD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;gBAClB,mGAAmG;gBACnG,yGAAyG;gBAEzG,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;gBAC3C,MAAM,CAAC,QAAQ,CAAC;oBACZ,oDAAoD;oBACpD,OAAO,EAAE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;iBACvF,CAAC,CAAC;YACP,CAAC;YAED,IAAI,MAAM,CAAC,kBAAkB,EAAE,CAAC;gBAC5B,6GAA6G;gBAC7G,yGAAyG;gBACzG,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAmB;oBAC3E,oDAAoD;oBACpD,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;iBAChE,CAAA,CAAC,CAAC;gBAEH,mDAAmD;gBACnD,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;gBAChE,cAAc,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;oBACjC,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,OAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;gBACjE,CAAC,CAAC,CAAC;YACP,CAAC;QACL,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;IAC1B,CAAC;IAED;;;;;OAKG;IACH,mBAAmB,CAAC,MAAoC;QACpD,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YACnB,OAAO;QACX,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC3C,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAC/C,IAAI,CAAC,0BAA0B,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;YACxD,IAAI,CAAC,mCAAmC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;IACP,CAAC;IAES,0BAA0B,CAAC,OAAe,EAAE,aAAkB;QACpE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,aAAa,CAAC;IAC3C,CAAC;IAED;;;;;MAKE;IACF,KAAK,CAAC,gBAAgB,CAAC,QAAgB,EAAE,aAAqB;QAC1D,MAAM,IAAI,CAAC,KAAK,CAAC;QAEjB,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,aAAa,CAAC,CAAC;QACrD,CAAC;IACL,CAAC;IAES,aAAa,CAAC,UAAkB;QACtC,OAAO,GAAG,UAAU,EAAE,CAAC;IAC3B,CAAC;IAED,IAAI,4BAA4B;QAC5B,OAAO,IAAI,CAAC,mCAAmC,CAAC,KAAK,CAAC;IAC1D,CAAC;CACJ"}