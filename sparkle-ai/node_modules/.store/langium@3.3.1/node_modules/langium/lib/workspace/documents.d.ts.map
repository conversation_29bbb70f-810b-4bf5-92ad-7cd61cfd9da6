{"version": 3, "file": "documents.d.ts", "sourceRoot": "", "sources": ["../../src/workspace/documents.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAEhF;;;;;;GAMG;AACH,OAAO,EAAE,YAAY,EAAE,MAAM,oCAAoC,CAAC;AAElE,OAAO,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,6BAA6B,CAAC;AACrE,OAAO,KAAK,EAAE,kBAAkB,EAAE,MAAM,2BAA2B,CAAC;AACpE,OAAO,KAAK,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AAC9E,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,KAAK,EAAE,yBAAyB,EAAE,MAAM,gBAAgB,CAAC;AAChE,OAAO,KAAK,EAAE,OAAO,EAAE,kBAAkB,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AACzF,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AACjD,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAC9C,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAE7D,OAAO,EAAE,GAAG,EAAE,MAAM,uBAAuB,CAAC;AAE5C;;;GAGG;AACH,MAAM,WAAW,eAAe,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO;IACxD,4DAA4D;IAC5D,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC;IAClB,sEAAsE;IACtE,QAAQ,CAAC,YAAY,EAAE,YAAY,CAAC;IACpC,wCAAwC;IACxC,KAAK,EAAE,aAAa,CAAC;IACrB,uGAAuG;IACvG,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;IAC5B,+CAA+C;IAC/C,iBAAiB,CAAC,EAAE,iBAAiB,CAAC;IACtC,sEAAsE;IACtE,UAAU,EAAE,SAAS,EAAE,CAAC;IACxB,qCAAqC;IACrC,WAAW,CAAC,EAAE,UAAU,EAAE,CAAA;CAC7B;AAED;;;GAGG;AACH,oBAAY,aAAa;IACrB;;;OAGG;IACH,OAAO,IAAI;IACX;;;;OAIG;IACH,MAAM,IAAI;IACV;;;OAGG;IACH,cAAc,IAAI;IAClB;;;;;;OAMG;IACH,cAAc,IAAI;IAClB;;;OAGG;IACH,MAAM,IAAI;IACV;;;;OAIG;IACH,iBAAiB,IAAI;IACrB;;;OAGG;IACH,SAAS,IAAI;CAChB;AAED;;;GAGG;AACH,MAAM,MAAM,iBAAiB,GAAG,QAAQ,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAA;AAErE,MAAM,WAAW,eAAe;IAC5B,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAA;IACrB,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAA;IACvB,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAA;IACvB,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAA;CACvB;AAED;;;GAGG;AACH,MAAM,MAAM,oBAAoB,GAAG;IAC/B,GAAG,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,GAAG,YAAY,GAAG,SAAS,CAAA;CACnD,CAAA;AAED;;;;;;GAMG;AACH,MAAM,WAAW,sBAAsB;IACnC;;OAEG;IACH,gBAAgB,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC,EAAE,aAAa,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;IAClI;;OAEG;IACH,gBAAgB,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,EAAE,GAAG,GAAG,SAAS,EAAE,iBAAiB,EAAE,iBAAiB,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;IAEnK;;OAEG;IACH,UAAU,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,EAAE,aAAa,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;IAC7G;;OAEG;IACH,UAAU,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,iBAAiB,EAAE,iBAAiB,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;IAEnI;;OAEG;IACH,SAAS,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;IAE/E;;OAEG;IACH,OAAO,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,iBAAiB,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;IAEnH;;;;;;OAMG;IACH,MAAM,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,iBAAiB,EAAE,iBAAiB,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAA;CACvI;AAED,qBAAa,6BAA8B,YAAW,sBAAsB;IAExE,SAAS,CAAC,QAAQ,CAAC,eAAe,EAAE,eAAe,CAAC;IACpD,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,oBAAoB,CAAC;IACxD,SAAS,CAAC,QAAQ,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;gBAE9C,QAAQ,EAAE,yBAAyB;IAMzC,OAAO,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,iBAAiB,oBAAyB,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;IAK7H,gBAAgB,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC,EAAE,aAAa,GAAG,eAAe,CAAC,CAAC,CAAC;IACjI,gBAAgB,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,EAAE,GAAG,GAAG,SAAS,EAAE,iBAAiB,EAAE,iBAAiB,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;IAUlK,UAAU,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,EAAE,aAAa,GAAG,eAAe,CAAC,CAAC,CAAC;IAC5G,UAAU,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,iBAAiB,EAAE,iBAAiB,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;IASlI,SAAS,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,GAAG,eAAe,CAAC,CAAC,CAAC;IAI9E,SAAS,CAAC,MAAM,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,GAAG,YAAY,GAAG;QAAE,MAAM,EAAE,CAAC,CAAA;KAAE,EAAE,OAAO,CAAC,EAAE,aAAa,GAAG,eAAe,CAAC,CAAC,CAAC;cAepI,WAAW,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,GAAG,YAAY,EAAE,WAAW,EAAE,iBAAiB,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;IAU/J;;;;;;;;;;OAUG;IACH,SAAS,CAAC,qBAAqB,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,IAAI,CAAC,EAAE,MAAM,GAAG,eAAe,CAAC,CAAC,CAAC;IA0B7J,MAAM,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE,iBAAiB,EAAE,iBAAiB,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;IAmCnJ,SAAS,CAAC,KAAK,CAAC,CAAC,SAAS,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,aAAa,GAAG,WAAW,CAAC,CAAC,CAAC;IAKnG,SAAS,CAAC,UAAU,CAAC,CAAC,SAAS,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,iBAAiB,EAAE,iBAAiB,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAK9H,SAAS,CAAC,wBAAwB,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,MAAM,GAAG,MAAM,YAAY;CASlF;AAED;;GAEG;AACH,MAAM,WAAW,gBAAgB;IAE7B;;OAEG;IACH,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAC,eAAe,CAAC,CAAA;IAErC;;;OAGG;IACH,WAAW,CAAC,QAAQ,EAAE,eAAe,GAAG,IAAI,CAAC;IAE7C;;OAEG;IACH,WAAW,CAAC,GAAG,EAAE,GAAG,GAAG,eAAe,GAAG,SAAS,CAAC;IAEnD;;;OAGG;IACH,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,iBAAiB,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;IAE/F;;;;;OAKG;IACH,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,GAAG,eAAe,CAAC;IAExD;;;;;;OAMG;IACH,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,iBAAiB,EAAE,iBAAiB,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;IAEvG;;OAEG;IACH,WAAW,CAAC,GAAG,EAAE,GAAG,GAAG,OAAO,CAAC;IAE/B;;;;;;OAMG;IACH,kBAAkB,CAAC,GAAG,EAAE,GAAG,GAAG,eAAe,GAAG,SAAS,CAAC;IAE1D;;;;;;OAMG;IACH,cAAc,CAAC,GAAG,EAAE,GAAG,GAAG,eAAe,GAAG,SAAS,CAAC;CACzD;AAED,qBAAa,uBAAwB,YAAW,gBAAgB;IAE5D,SAAS,CAAC,QAAQ,CAAC,sBAAsB,EAAE,sBAAsB,CAAC;IAClE,SAAS,CAAC,QAAQ,CAAC,eAAe,EAAE,eAAe,CAAC;IAEpD,SAAS,CAAC,QAAQ,CAAC,WAAW,EAAE,GAAG,CAAC,MAAM,EAAE,eAAe,CAAC,CAAa;gBAE7D,QAAQ,EAAE,yBAAyB;IAK/C,IAAI,GAAG,IAAI,MAAM,CAAC,eAAe,CAAC,CAEjC;IAED,WAAW,CAAC,QAAQ,EAAE,eAAe,GAAG,IAAI;IAQ5C,WAAW,CAAC,GAAG,EAAE,GAAG,GAAG,eAAe,GAAG,SAAS;IAK5C,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,iBAAiB,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,eAAe,CAAC;IAUpG,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,GAAG,eAAe;IACvD,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,iBAAiB,EAAE,iBAAiB,GAAG,OAAO,CAAC,eAAe,CAAC;IActG,WAAW,CAAC,GAAG,EAAE,GAAG,GAAG,OAAO;IAI9B,kBAAkB,CAAC,GAAG,EAAE,GAAG,GAAG,eAAe,GAAG,SAAS;IAazD,cAAc,CAAC,GAAG,EAAE,GAAG,GAAG,eAAe,GAAG,SAAS;CASxD"}