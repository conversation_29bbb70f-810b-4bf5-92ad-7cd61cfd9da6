{"version": 3, "file": "configuration.d.ts", "sourceRoot": "", "sources": ["../../src/workspace/configuration.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAEhF,OAAO,EAAE,OAAO,EAAE,MAAM,mBAAmB,CAAC;AAC5C,OAAO,KAAK,EACR,iBAAiB,EACjB,4BAA4B,EAC5B,yCAAyC,EACzC,UAAU,EACV,KAAK,EACL,gBAAgB,EAChB,iBAAiB,EACpB,MAAM,gCAAgC,CAAC;AACxC,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,KAAK,EAAE,yBAAyB,EAAE,MAAM,gBAAgB,CAAC;AAChE,OAAO,EAAE,QAAQ,EAAE,MAAM,2BAA2B,CAAC;AAIrD,MAAM,WAAW,qBAAqB;IAElC;;OAEG;IACH,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAE9B;;;OAGG;IACH,UAAU,CAAC,MAAM,EAAE,gBAAgB,GAAG,IAAI,CAAC;IAE3C;;;OAGG;IACH,WAAW,CAAC,MAAM,EAAE,8BAA8B,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAEnE;;;;;OAKG;IACH,gBAAgB,CAAC,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;IAExE;;;;;OAKG;IACH,mBAAmB,CAAC,MAAM,EAAE,4BAA4B,GAAG,IAAI,CAAC;IAEhE;;OAEG;IACH,4BAA4B,CAAC,QAAQ,EAAE,kCAAkC,GAAG,UAAU,CAAA;CACzF;AAED,MAAM,WAAW,8BAA+B,SAAQ,iBAAiB;IACrE,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,yCAAyC,KAAK,IAAI,CAAC;IACvE,kBAAkB,CAAC,EAAE,CAAC,aAAa,EAAE,iBAAiB,EAAE,KAAK,OAAO,CAAC,GAAG,CAAC,CAAA;CAC5E;AAED,MAAM,WAAW,0BAA0B;IACvC;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;OAEG;IACH,aAAa,EAAE,GAAG,CAAC;CACtB;AAED,MAAM,MAAM,kCAAkC,GAAG,CAAC,MAAM,EAAE,0BAA0B,KAAK,IAAI,CAAC;AAE9F;;GAEG;AACH,qBAAa,4BAA6B,YAAW,qBAAqB;IAEtE,SAAS,CAAC,QAAQ,CAAC,eAAe,EAAE,eAAe,CAAC;IACpD,SAAS,CAAC,QAAQ,CAAC,MAAM,iBAAwB;IACjD,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAM;IAC7D,SAAS,CAAC,eAAe,UAAS;IAClC,SAAS,CAAC,mCAAmC,sCAA6C;gBAE9E,QAAQ,EAAE,yBAAyB;IAI/C,IAAI,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC,CAEzB;IAED,UAAU,CAAC,MAAM,EAAE,gBAAgB,GAAG,IAAI;IAIpC,WAAW,CAAC,MAAM,EAAE,8BAA8B,GAAG,OAAO,CAAC,IAAI,CAAC;IA+BxE;;;;;OAKG;IACH,mBAAmB,CAAC,MAAM,EAAE,4BAA4B,GAAG,IAAI;IAW/D,SAAS,CAAC,0BAA0B,CAAC,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,GAAG,IAAI;IAI/E;;;;;MAKE;IACI,gBAAgB,CAAC,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAS7E,SAAS,CAAC,aAAa,CAAC,UAAU,EAAE,MAAM,GAAG,MAAM;IAInD,IAAI,4BAA4B,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAEpE;CACJ"}