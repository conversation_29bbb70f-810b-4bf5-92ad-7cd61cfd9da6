#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="$basedir/../../../../../vscode-languageserver@9.0.1/node_modules"
else
  export NODE_PATH="$NODE_PATH:$basedir/../../../../../vscode-languageserver@9.0.1/node_modules"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../vscode-languageserver@9.0.1/node_modules/vscode-languageserver/bin/installServerIntoExtension" "$@"
else
  exec node  "$basedir/../../../../../vscode-languageserver@9.0.1/node_modules/vscode-languageserver/bin/installServerIntoExtension" "$@"
fi
