import React, { useState, useRef } from 'react';
import ReactECharts from 'echarts-for-react';

interface EChartsRendererProps {
  config: any;
  id?: string;
  className?: string;
  height?: number;
}

const EChartsRenderer: React.FC<EChartsRendererProps> = ({
  config,
  className = '',
  height = 400
}) => {
  const [error, setError] = useState<string | null>(null);
  const chartRef = useRef<ReactECharts>(null);

  // 验证和处理配置
  const processConfig = (rawConfig: any) => {
    try {
      let processedConfig = rawConfig;

      // 如果是字符串，尝试解析为 JSON
      if (typeof rawConfig === 'string') {
        processedConfig = JSON.parse(rawConfig);
      }

      // 添加默认配置
      const defaultConfig = {
        animation: true,
        animationDuration: 1000,
        backgroundColor: 'transparent',
        textStyle: {
          fontFamily: 'Arial, sans-serif',
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: 'transparent',
          textStyle: {
            color: '#fff',
          },
        },
        legend: {
          textStyle: {
            color: '#666',
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
      };

      // 合并配置
      return { ...defaultConfig, ...processedConfig };
    } catch (err) {
      console.error('ECharts config processing error:', err);
      setError(err instanceof Error ? err.message : 'Invalid chart configuration');
      return null;
    }
  };

  const processedConfig = processConfig(config);

  const copyConfig = () => {
    const configString = JSON.stringify(config, null, 2);
    navigator.clipboard.writeText(configString);
  };

  const downloadImage = () => {
    const chartInstance = chartRef.current?.getEchartsInstance();
    if (chartInstance) {
      const url = chartInstance.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff',
      });

      const link = document.createElement('a');
      link.href = url;
      link.download = `echarts-${Date.now()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const refreshChart = () => {
    const chartInstance = chartRef.current?.getEchartsInstance();
    if (chartInstance) {
      chartInstance.resize();
    }
  };

  // 获取图表类型
  const getChartType = (config: any): string => {
    if (!config || !config.series) return 'unknown';
    
    const series = Array.isArray(config.series) ? config.series[0] : config.series;
    return series.type || 'unknown';
  };

  const chartType = processedConfig ? getChartType(processedConfig) : 'unknown';

  // 图表类型图标映射
  const getChartIcon = (type: string): string => {
    const iconMap: { [key: string]: string } = {
      line: '📈',
      bar: '📊',
      pie: '🥧',
      scatter: '🔵',
      radar: '🕸️',
      gauge: '⏱️',
      funnel: '🔻',
      tree: '🌳',
      treemap: '🗂️',
      sunburst: '☀️',
      sankey: '🌊',
      heatmap: '🔥',
      graph: '🕸️',
      unknown: '📋',
    };
    return iconMap[type] || iconMap.unknown;
  };

  if (error || !processedConfig) {
    return (
      <div className={`echarts-error border border-red-300 rounded-lg p-4 bg-red-50 dark:bg-red-900/20 ${className}`}>
        <div className="flex items-center justify-between mb-2">
          <h4 className="text-red-800 dark:text-red-200 font-semibold">ECharts 图表渲染错误</h4>
          <button
            onClick={copyConfig}
            className="text-xs bg-red-500 hover:bg-red-600 text-white px-2 py-1 rounded"
          >
            复制配置
          </button>
        </div>
        <p className="text-red-600 dark:text-red-300 text-sm mb-3">
          {error || '无效的图表配置'}
        </p>
        <details className="text-sm">
          <summary className="cursor-pointer text-red-700 dark:text-red-300 hover:text-red-800 dark:hover:text-red-200">
            查看配置
          </summary>
          <pre className="mt-2 p-2 bg-red-100 dark:bg-red-800/30 rounded text-red-800 dark:text-red-200 overflow-x-auto">
            {JSON.stringify(config, null, 2)}
          </pre>
        </details>
      </div>
    );
  }

  return (
    <div className={`echarts-container border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden ${className}`}>
      <div className="flex items-center justify-between bg-gray-100 dark:bg-gray-800 px-4 py-2">
        <span className="text-sm text-gray-600 dark:text-gray-400 font-medium">
          {getChartIcon(chartType)} ECharts 图表 ({chartType})
        </span>
        <div className="flex space-x-2">
          <button
            onClick={refreshChart}
            className="text-xs bg-gray-500 hover:bg-gray-600 text-white px-2 py-1 rounded"
            title="刷新图表"
          >
            刷新
          </button>
          <button
            onClick={copyConfig}
            className="text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded"
            title="复制配置"
          >
            复制
          </button>
          <button
            onClick={downloadImage}
            className="text-xs bg-green-500 hover:bg-green-600 text-white px-2 py-1 rounded"
            title="下载图片"
          >
            下载
          </button>
        </div>
      </div>
      
      <div className="p-4 bg-white dark:bg-gray-900">
        <ReactECharts
          ref={chartRef}
          option={processedConfig}
          style={{ height: `${height}px`, width: '100%' }}
          opts={{
            renderer: 'canvas'
          }}
          onEvents={{
            click: (params: any) => {
              console.log('Chart clicked:', params);
            },
          }}
        />
      </div>
    </div>
  );
};

// 用于检测文本中的 ECharts 配置块
export const extractEChartsBlocks = (content: string): Array<{ id: string; config: any; type: string }> => {
  const echartsRegex = /```echarts\n([\s\S]*?)\n```/g;
  const blocks: Array<{ id: string; config: any; type: string }> = [];
  let match;

  while ((match = echartsRegex.exec(content)) !== null) {
    try {
      const configString = match[1].trim();
      const config = JSON.parse(configString);
      const id = `echarts-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      // 检测图表类型
      let type = 'unknown';
      if (config.series) {
        const series = Array.isArray(config.series) ? config.series[0] : config.series;
        type = series.type || 'unknown';
      }

      blocks.push({ id, config, type });
    } catch (err) {
      console.error('Failed to parse ECharts config:', err);
      // 即使解析失败也添加到块中，让组件处理错误
      const id = `echarts-error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      blocks.push({ id, config: match[1].trim(), type: 'error' });
    }
  }

  return blocks;
};

// 预定义的图表模板
export const chartTemplates = {
  line: {
    title: { text: '折线图示例' },
    xAxis: { type: 'category', data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] },
    yAxis: { type: 'value' },
    series: [{ data: [820, 932, 901, 934, 1290, 1330, 1320], type: 'line' }],
  },
  bar: {
    title: { text: '柱状图示例' },
    xAxis: { type: 'category', data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] },
    yAxis: { type: 'value' },
    series: [{ data: [120, 200, 150, 80, 70, 110, 130], type: 'bar' }],
  },
  pie: {
    title: { text: '饼图示例' },
    series: [{
      name: 'Access From',
      type: 'pie',
      radius: '50%',
      data: [
        { value: 1048, name: 'Search Engine' },
        { value: 735, name: 'Direct' },
        { value: 580, name: 'Email' },
        { value: 484, name: 'Union Ads' },
        { value: 300, name: 'Video Ads' },
      ],
    }],
  },
};

export default EChartsRenderer;
