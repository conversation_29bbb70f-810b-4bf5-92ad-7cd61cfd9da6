import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import Button from '../Common/Button';
import Input from '../Common/Input';
import { ToolInfo } from '../../types/tools';

interface ToolSettingsProps {
  onClose?: () => void;
}

const ToolSettings: React.FC<ToolSettingsProps> = ({ onClose }) => {
  const [tools, setTools] = useState<ToolInfo[]>([]);
  const [apiKeys, setApiKeys] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 加载工具列表和设置
  useEffect(() => {
    loadToolSettings();
  }, []);

  const loadToolSettings = async () => {
    try {
      setLoading(true);
      setError(null);

      // 获取可用工具列表
      const toolList = await invoke<ToolInfo[]>('get_available_tools');
      setTools(toolList);

      // 获取当前设置
      const settings = await invoke<any>('get_settings');
      if (settings.ai_settings) {
        setApiKeys(settings.ai_settings.api_keys || {});
      }
    } catch (err) {
      console.error('Failed to load tool settings:', err);
      setError(err instanceof Error ? err.message : '加载设置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleToolToggle = async (toolName: string, enabled: boolean) => {
    try {
      await invoke('update_tool_permission', {
        toolName,
        enabled,
      });

      // 更新本地状态
      setTools(prev => prev.map(tool => 
        tool.name === toolName ? { ...tool, enabled } : tool
      ));
    } catch (err) {
      console.error('Failed to update tool permission:', err);
      setError(err instanceof Error ? err.message : '更新工具权限失败');
    }
  };

  const handleApiKeyChange = (key: string, value: string) => {
    setApiKeys(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleSaveApiKeys = async () => {
    try {
      setSaving(true);
      setError(null);

      await invoke('update_api_keys', { apiKeys });
      
      // 显示成功提示
      alert('API 密钥保存成功');
    } catch (err) {
      console.error('Failed to save API keys:', err);
      setError(err instanceof Error ? err.message : '保存 API 密钥失败');
    } finally {
      setSaving(false);
    }
  };

  const testTool = async (toolName: string) => {
    try {
      const testParams = getTestParams(toolName);
      const result = await invoke('execute_tool', {
        toolName,
        params: testParams,
      });
      
      alert(`工具测试成功: ${JSON.stringify(result, null, 2)}`);
    } catch (err) {
      console.error('Tool test failed:', err);
      alert(`工具测试失败: ${err instanceof Error ? err.message : '未知错误'}`);
    }
  };

  const getTestParams = (toolName: string): any => {
    switch (toolName) {
      case 'safe_shell':
        return { command: 'echo "Hello World"' };
      case 'web_search':
        return { query: 'test', limit: 1 };
      case 'gaode_map':
        return { action: 'weather', location: '北京' };
      case 'siyuan_notes':
        return { action: 'test_connection' };
      default:
        return {};
    }
  };

  const getToolIcon = (toolName: string): string => {
    const iconMap: Record<string, string> = {
      safe_shell: '🖥️',
      web_search: '🔍',
      gaode_map: '🗺️',
      siyuan_notes: '📝',
    };
    return iconMap[toolName] || '🔧';
  };

  const getApiKeyFields = () => {
    return [
      { key: 'openai', label: 'OpenAI API Key', placeholder: 'sk-...' },
      { key: 'anthropic', label: 'Anthropic API Key', placeholder: 'sk-ant-...' },
      { key: 'bing_search', label: 'Bing Search API Key', placeholder: 'Bing 搜索 API 密钥' },
      { key: 'gaode_map', label: '高德地图 API Key', placeholder: '高德地图 API 密钥' },
    ];
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-2">加载中...</span>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">工具设置</h2>
        {onClose && (
          <Button variant="ghost" onClick={onClose}>
            ✕
          </Button>
        )}
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-red-800 dark:text-red-200">{error}</p>
        </div>
      )}

      {/* 工具权限设置 */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">工具权限</h3>
        <div className="space-y-4">
          {tools.map((tool) => (
            <div
              key={tool.name}
              className="flex items-center justify-between p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg"
            >
              <div className="flex items-center space-x-3">
                <span className="text-2xl">{getToolIcon(tool.name)}</span>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    {tool.name}
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {tool.description}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => testTool(tool.name)}
                  disabled={!tool.enabled}
                >
                  测试
                </Button>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={tool.enabled}
                    onChange={(e) => handleToolToggle(tool.name, e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* API 密钥设置 */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">API 密钥</h3>
        <div className="space-y-4">
          {getApiKeyFields().map((field) => (
            <div key={field.key}>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {field.label}
              </label>
              <Input
                type="password"
                value={apiKeys[field.key] || ''}
                onChange={(e) => handleApiKeyChange(field.key, e.target.value)}
                placeholder={field.placeholder}
                className="w-full"
              />
            </div>
          ))}
        </div>
        <div className="mt-4">
          <Button
            onClick={handleSaveApiKeys}
            disabled={saving}
            className="w-full sm:w-auto"
          >
            {saving ? '保存中...' : '保存 API 密钥'}
          </Button>
        </div>
      </div>

      {/* 安全设置说明 */}
      <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
        <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">
          ⚠️ 安全提示
        </h4>
        <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
          <li>• API 密钥将安全存储在本地，不会上传到服务器</li>
          <li>• 启用工具前请确保了解其功能和潜在风险</li>
          <li>• safe_shell 工具具有系统命令执行权限，请谨慎使用</li>
          <li>• 建议定期更新 API 密钥以确保安全</li>
        </ul>
      </div>
    </div>
  );
};

export default ToolSettings;
